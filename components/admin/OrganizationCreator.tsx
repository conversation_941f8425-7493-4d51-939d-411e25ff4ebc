"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Building2,
  Plus,
  Loader2,
  CheckCircle,
  AlertCircle,
  Trash2
} from "lucide-react";

interface DomainInput {
  domain: string;
  auto_assign_role: string;
  verified: boolean;
  admin_approved: boolean;
}

interface OrganizationCreatorProps {
  onSuccess?: (organization: any) => void;
  onError?: (error: string) => void;
  className?: string;
  apiEndpoint?: string;
}

export default function OrganizationCreator({
  onSuccess,
  onError,
  className = "",
  apiEndpoint = "/api/admin/organizations"
}: OrganizationCreatorProps) {
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    plan: 'free',
    status: 'active',
    billing_email: '',
    max_agents: 3,
    max_calls_per_month: 1000,
    settings: '{}'
  });

  const [domains, setDomains] = useState<DomainInput[]>([
    { domain: '', auto_assign_role: 'member', verified: false, admin_approved: false }
  ]);

  // Domain validation state
  const [domainValidation, setDomainValidation] = useState<{[key: number]: {loading: boolean, result?: any}}>({});

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-generate slug from name
    if (field === 'name' && !formData.slug) {
      const slug = value.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
      setFormData(prev => ({
        ...prev,
        slug
      }));
    }
  };

  const addDomain = () => {
    setDomains(prev => [
      ...prev,
      { domain: '', auto_assign_role: 'member', verified: false, admin_approved: false }
    ]);
  };

  const removeDomain = (index: number) => {
    setDomains(prev => prev.filter((_, i) => i !== index));
  };

  const updateDomain = (index: number, field: string, value: any) => {
    setDomains(prev => prev.map((domain, i) =>
      i === index ? { ...domain, [field]: value } : domain
    ));

    // Validate domain when domain field changes
    if (field === 'domain' && value.trim()) {
      validateDomain(index, value.trim());
    } else if (field === 'domain' && !value.trim()) {
      // Clear validation when domain is empty
      setDomainValidation(prev => {
        const updated = { ...prev };
        delete updated[index];
        return updated;
      });
    }
  };

  const validateDomain = async (index: number, domain: string) => {
    if (!domain.trim()) return;

    // Set loading state
    setDomainValidation(prev => ({
      ...prev,
      [index]: { loading: true }
    }));

    try {
      const response = await fetch('/api/admin/domains/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ domain: domain.trim() }),
      });

      const result = await response.json();

      setDomainValidation(prev => ({
        ...prev,
        [index]: { loading: false, result }
      }));
    } catch (error) {
      console.error('Domain validation error:', error);
      setDomainValidation(prev => ({
        ...prev,
        [index]: {
          loading: false,
          result: {
            valid: false,
            error: 'validation_error',
            message: 'Unable to validate domain. Please try again.'
          }
        }
      }));
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      plan: 'free',
      status: 'active',
      billing_email: '',
      max_agents: 3,
      max_calls_per_month: 1000,
      settings: '{}'
    });
    setDomains([
      { domain: '', auto_assign_role: 'member', verified: false, admin_approved: false }
    ]);
    setError(null);
    setSuccess(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setCreating(true);
    setError(null);
    setSuccess(null);

    try {
      // Validate settings JSON
      let parsedSettings = {};
      if (formData.settings.trim()) {
        try {
          parsedSettings = JSON.parse(formData.settings);
        } catch {
          throw new Error('Invalid JSON in settings field');
        }
      }

      // Filter out empty domains and check for validation errors
      const validDomains = domains.filter(d => d.domain.trim());

      // Check if any domains have validation errors
      const hasInvalidDomains = validDomains.some((domain, index) => {
        const validation = domainValidation[index];
        return validation?.result && !validation.result.valid;
      });

      if (hasInvalidDomains) {
        throw new Error('Please fix domain validation errors before creating the organization');
      }

      const payload = {
        ...formData,
        settings: parsedSettings,
        domains: validDomains
      };

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create organization');
      }

      const successMessage = 'Organization created successfully!';
      setSuccess(successMessage);

      // Call success callback
      if (onSuccess) {
        onSuccess(result.organization);
      }

      // Reset form after successful creation
      resetForm();

    } catch (err: any) {
      console.error('Error creating organization:', err);
      const errorMessage = err.message || 'Failed to create organization';
      setError(errorMessage);

      // Call error callback
      if (onError) {
        onError(errorMessage);
      }
    } finally {
      setCreating(false);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Create New Organization
        </CardTitle>
        <CardDescription>
          Set up a new organization with domains and access controls
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert className="mb-6 border-red-800 bg-red-900/20">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-red-400">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-6 border-green-800 bg-green-900/20">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription className="text-green-400">
              {success}
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Organization Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Acme Corporation"
                required
              />
            </div>
            <div>
              <Label htmlFor="slug">Slug</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => handleInputChange('slug', e.target.value)}
                placeholder="acme-corporation"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Auto-generated from name if left empty
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="plan">Plan</Label>
              <Select value={formData.plan} onValueChange={(value) => handleInputChange('plan', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="free">Free</SelectItem>
                  <SelectItem value="starter">Starter</SelectItem>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="enterprise">Enterprise</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="suspended">Suspended</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="billing_email">Billing Email</Label>
              <Input
                id="billing_email"
                type="email"
                value={formData.billing_email}
                onChange={(e) => handleInputChange('billing_email', e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          {/* Limits */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="max_agents">Max Agents</Label>
              <Input
                id="max_agents"
                type="number"
                min="1"
                value={formData.max_agents}
                onChange={(e) => handleInputChange('max_agents', parseInt(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="max_calls_per_month">Max Calls per Month</Label>
              <Input
                id="max_calls_per_month"
                type="number"
                min="1"
                value={formData.max_calls_per_month}
                onChange={(e) => handleInputChange('max_calls_per_month', parseInt(e.target.value))}
              />
            </div>
          </div>

          {/* Domains */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <Label>Authorized Domains</Label>
              <Button type="button" onClick={addDomain} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Domain
              </Button>
            </div>

            <div className="space-y-3">
              {domains.map((domain, index) => {
                const validation = domainValidation[index];
                const isValidating = validation?.loading;
                const validationResult = validation?.result;
                const hasError = validationResult && !validationResult.valid;
                const isValid = validationResult && validationResult.valid;

                return (
                  <div key={index} className="space-y-2">
                    <div className="grid grid-cols-12 gap-2 items-end">
                      <div className="col-span-4">
                        <div className="relative">
                          <Input
                            placeholder="company.com"
                            value={domain.domain}
                            onChange={(e) => updateDomain(index, 'domain', e.target.value)}
                            className={`${hasError ? 'border-red-500' : isValid ? 'border-green-500' : ''}`}
                          />
                          {isValidating && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                              <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                            </div>
                          )}
                          {isValid && !isValidating && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                              <div className="h-4 w-4 text-green-500">✓</div>
                            </div>
                          )}
                          {hasError && !isValidating && (
                            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                              <div className="h-4 w-4 text-red-500">✗</div>
                            </div>
                          )}
                        </div>
                      </div>
                  <div className="col-span-2">
                    <Select
                      value={domain.auto_assign_role}
                      onValueChange={(value) => updateDomain(index, 'auto_assign_role', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="member">Member</SelectItem>
                        <SelectItem value="viewer">Viewer</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-2 flex gap-2">
                    <label className="flex items-center space-x-1">
                      <input
                        type="checkbox"
                        checked={domain.verified}
                        onChange={(e) => updateDomain(index, 'verified', e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-xs">Verified</span>
                    </label>
                  </div>
                  <div className="col-span-2 flex gap-2">
                    <label className="flex items-center space-x-1">
                      <input
                        type="checkbox"
                        checked={domain.admin_approved}
                        onChange={(e) => updateDomain(index, 'admin_approved', e.target.checked)}
                        className="rounded"
                      />
                      <span className="text-xs">Approved</span>
                    </label>
                  </div>
                  <div className="col-span-2">
                    {domains.length > 1 && (
                      <Button
                        type="button"
                        onClick={() => removeDomain(index)}
                        variant="outline"
                        size="sm"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
                {hasError && validationResult?.message && (
                  <div className="text-sm text-red-600 mt-1">
                    {validationResult.message}
                  </div>
                )}
              </div>
                );
              })}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              Users with email addresses from these domains will be automatically assigned to this organization
            </p>
          </div>

          <div className="flex gap-4">
            <Button type="submit" disabled={creating} className="flex-1">
              {creating ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Creating Organization...
                </>
              ) : (
                <>
                  <Building2 className="h-4 w-4 mr-2" />
                  Create Organization
                </>
              )}
            </Button>
            <Button type="button" onClick={resetForm} variant="outline">
              Reset
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
