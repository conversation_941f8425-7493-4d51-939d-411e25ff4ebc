"use client";

import React, { createContext, useContext } from 'react';

// Mock user for development
const mockUser = {
  id: 'dev-user-123',
  email: '<EMAIL>',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  email_confirmed_at: new Date().toISOString(),
  last_sign_in_at: new Date().toISOString(),
  app_metadata: {},
  user_metadata: {},
  aud: 'authenticated',
  role: 'authenticated'
};

interface DomainValidationResult {
  allowed: boolean;
  reason?: string;
  message: string;
  domain?: string;
  organization_id?: string;
  organization_name?: string;
  auto_assign_role?: string;
}

interface AuthContextType {
  user: typeof mockUser | null;
  loading: boolean;
  signInWithMagicLink: (email: string) => Promise<void>;
  signOut: () => Promise<void>;
  validateDomain: (email: string) => Promise<DomainValidationResult>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface NoAuthProviderProps {
  children: React.ReactNode;
}

export function NoAuthProvider({ children }: NoAuthProviderProps) {
  const signInWithMagicLink = async (email: string) => {
    console.log('Mock sign in with magic link:', email);
    // Do nothing in development mode
  };

  const signOut = async () => {
    console.log('Mock sign out');
    // Do nothing in development mode
  };

  const validateDomain = async (email: string): Promise<DomainValidationResult> => {
    console.log('Mock domain validation:', email);
    return {
      allowed: true,
      message: 'Domain validation bypassed in development mode',
      domain: email.split('@')[1],
      organization_id: 'dev-org-123',
      organization_name: 'Development Organization',
      auto_assign_role: 'admin'
    };
  };

  const value = {
    user: mockUser,
    loading: false,
    signInWithMagicLink,
    signOut,
    validateDomain,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
