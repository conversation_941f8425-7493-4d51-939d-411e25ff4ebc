"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';
import { User } from '@supabase/supabase-js';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, User as UserIcon, Mail, Lock, CheckCircle, AlertCircle, Building2 } from "lucide-react";

interface DomainValidationResult {
  allowed: boolean;
  reason?: string;
  message: string;
  domain?: string;
  organization_id?: string;
  organization_name?: string;
  auto_assign_role?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signInWithMagicLink: (email: string) => Promise<void>;
  signOut: () => Promise<void>;
  validateDomain: (email: string) => Promise<DomainValidationResult>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user ?? null);
      setLoading(false);
    };

    getSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signInWithMagicLink = async (email: string) => {
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });
    if (error) throw error;
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  const validateDomain = async (email: string): Promise<DomainValidationResult> => {
    const response = await fetch('/api/auth/validate-domain', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return errorData;
    }

    return response.json();
  };

  const value = {
    user,
    loading,
    signInWithMagicLink,
    signOut,
    validateDomain,
  };

  // Allow auth routes to render without authentication check
  const isAuthRoute = pathname?.startsWith('/auth/') || pathname === '/login' || pathname === '/create-account';

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user && !isAuthRoute) {
    return <AuthScreen />;
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

function AuthScreen() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [domainValidation, setDomainValidation] = useState<DomainValidationResult | null>(null);
  const [step, setStep] = useState<'email' | 'validation' | 'magic-link'>('email');

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // First validate the domain
      const response = await fetch('/api/auth/validate-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const validation = await response.json();
      setDomainValidation(validation);

      if (validation.allowed) {
        setStep('validation');
      } else {
        setError(validation.message);
      }
    } catch (err: any) {
      console.error('Domain validation error:', err);
      setError('Unable to validate your email domain. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleMagicLinkRequest = async () => {
    setLoading(true);
    setError(null);

    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) throw error;

      setSuccess(`Magic link sent to ${email}! Check your inbox and click the link to sign in.`);
      setStep('magic-link');
    } catch (err: any) {
      console.error('Magic link error:', err);
      setError(err.message || 'Failed to send magic link. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setStep('email');
    setEmail('');
    setError(null);
    setSuccess(null);
    setDomainValidation(null);
  };

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900 border-gray-800">
        <CardHeader className="text-center">
          <CardTitle className="text-white flex items-center justify-center gap-2">
            <UserIcon className="h-6 w-6" />
            VALabs Portal
          </CardTitle>
          <CardDescription className="text-gray-400">
            {step === 'email' && 'Enter your work email to get started'}
            {step === 'validation' && 'Confirm your organization'}
            {step === 'magic-link' && 'Check your email'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert className="mb-4 border-red-800 bg-red-900/20">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-red-400">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-4 border-green-800 bg-green-900/20">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription className="text-green-400">
                {success}
              </AlertDescription>
            </Alert>
          )}

          {step === 'email' && (
            <form onSubmit={handleEmailSubmit} className="space-y-4">
              <div>
                <Label htmlFor="email" className="text-gray-300">Work Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-500" />
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10 bg-gray-800 border-gray-700 text-white"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Use your work email address to access your organization
                </p>
              </div>
              <Button type="submit" disabled={loading} className="w-full">
                {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Continue
              </Button>
            </form>
          )}

          {step === 'validation' && domainValidation && (
            <div className="space-y-4">
              <div className="p-4 bg-blue-900/20 border border-blue-800 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Building2 className="h-5 w-5 text-blue-400" />
                  <span className="text-blue-400 font-medium">Organization Found</span>
                </div>
                <p className="text-white font-medium">{domainValidation.organization_name}</p>
                <p className="text-gray-400 text-sm">Domain: {domainValidation.domain}</p>
                <p className="text-gray-400 text-sm">Role: {domainValidation.auto_assign_role}</p>
              </div>

              <div className="space-y-2">
                <Button onClick={handleMagicLinkRequest} disabled={loading} className="w-full">
                  {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  Send Magic Link
                </Button>
                <Button onClick={resetForm} variant="outline" className="w-full">
                  Use Different Email
                </Button>
              </div>
            </div>
          )}

          {step === 'magic-link' && (
            <div className="space-y-4 text-center">
              <div className="p-4 bg-green-900/20 border border-green-800 rounded-lg">
                <Mail className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <p className="text-white font-medium">Check Your Email</p>
                <p className="text-gray-400 text-sm mt-1">
                  We sent a magic link to <strong>{email}</strong>
                </p>
              </div>

              <div className="text-sm text-gray-400">
                <p>Click the link in your email to sign in.</p>
                <p className="mt-2">Didn't receive it? Check your spam folder.</p>
              </div>

              <Button onClick={resetForm} variant="outline" className="w-full">
                Use Different Email
              </Button>
            </div>
          )}

          <div className="mt-6 pt-4 border-t border-gray-700">
            <p className="text-xs text-gray-500 text-center">
              Only users from registered organizations can access this portal.
              <br />
              Contact your administrator if you need access.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
