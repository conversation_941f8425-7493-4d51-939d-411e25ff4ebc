"use client"

import { <PERSON><PERSON><PERSON><PERSON>, MessageS<PERSON>re, Brain, Bot, ChevronDown, Share2, Building2, User, LogOut, Database } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { usePathname } from "next/navigation"
import { useOrganization } from "@/contexts/OrganizationContext"
import { useAuth } from "@/components/auth/AuthProvider"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

const menuSections = {
  analytics: [
    { icon: BarChart2, label: "Dashboard", href: "/" },
    { icon: MessageSquare, label: "Call History", href: "/calls" },
    { icon: Brain, label: "Intelligent Insights", href: "/insights", badge: "In Progress" },
  ],
  builder: [
    {
      icon: <PERSON><PERSON>,
      label: "AI Agent Builder",
      href: "/agent-builder",
      description: "Create and manage your AI agents",
      badge: "In Progress"
    },
    {
      icon: Share2,
      label: "Community AI Agents",
      href: "/agent-builder/community",
      description: "Explore and use community-created AI agents",
      badge: "New"
    }
  ],
  admin: [
    {
      icon: Building2,
      label: "Organization Admin",
      href: "/admin/organizations",
      description: "Create and manage organizations",
      badge: "Admin"
    },
    {
      icon: Database,
      label: "Agent Sync",
      href: "/admin/sync",
      description: "Synchronize agents with RetellAI",
      badge: "Admin"
    }
  ]
}

export default function Sidebar() {
  const pathname = usePathname()
  const { organization, loading, stats, isAdmin } = useOrganization()
  const { user, signOut } = useAuth()

  return (
    <div className="w-64 bg-background border-r border-border flex flex-col">
      <div className="p-4 border-b border-border">
        <Image
          src="/valabs_logo.png"
          alt="Virtual Assistant Labs Logo"
          width={150}
          height={50}
          className="mb-2"
        />

        {/* Organization Info */}
        {loading ? (
          <div className="flex items-center space-x-2">
            <div className="h-6 w-6 rounded-full bg-muted animate-pulse" />
            <div className="h-4 w-20 bg-muted animate-pulse rounded" />
          </div>
        ) : organization ? (
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Building2 className="h-5 w-5 text-primary" />
              <span className="text-sm font-medium text-foreground">{organization.name}</span>
              {isAdmin && (
                <span className="text-xs bg-primary/20 text-primary px-1.5 py-0.5 rounded">
                  Admin
                </span>
              )}
            </div>
            {stats && (
              <div className="text-xs text-muted-foreground">
                {stats.total_agents} agents • {stats.current_month_calls} calls this month
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <div className="h-6 w-6 rounded-full bg-muted" />
            <span className="text-sm font-medium text-foreground">VAL</span>
          </div>
        )}
      </div>
      <nav className="flex-1 p-4">
        {/* Real-Time Analytics Section */}
        <div className="mb-6">
          <h2 className="px-3 mb-2 text-sm font-semibold text-muted-foreground">Real-Time Analytics</h2>
          <ul className="space-y-1">
            {menuSections.analytics.map((item) => (
              <li key={item.label}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium group relative",
                    pathname === item.href
                      ? "bg-primary/10 text-primary"
                      : "text-muted-foreground hover:bg-muted"
                  )}
                >
                  <item.icon className="h-5 w-5" />
                  <span className="flex-1">{item.label}</span>
                  {item.badge && (
                    <span className="bg-yellow-100 text-yellow-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">
                      {item.badge}
                    </span>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* AI Agent Builder Section */}
        <div className="mb-6">
          <h2 className="px-3 mb-2 text-sm font-semibold text-muted-foreground">AI Agent Builder</h2>
          <ul className="space-y-1">
            {menuSections.builder.map((item) => (
              <li key={item.label}>
                <Link
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium group relative border-l-2 border-primary/50",
                    pathname === item.href
                      ? "bg-primary/10 text-primary"
                      : "text-muted-foreground hover:bg-muted"
                  )}
                >
                  <item.icon className="h-5 w-5" />
                  <span className="flex-1">{item.label}</span>
                  {item.description && (
                    <div className="absolute left-full ml-2 w-48 p-2 rounded-md bg-popover text-sm hidden group-hover:block shadow-lg border z-50">
                      {item.description}
                    </div>
                  )}
                  {item.badge && (
                    <span className={cn(
                      "text-xs font-medium px-2 py-0.5 rounded",
                      item.badge === "In Progress"
                        ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                        : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                    )}>
                      {item.badge}
                    </span>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Administration Section */}
        {isAdmin && (
          <div>
            <h2 className="px-3 mb-2 text-sm font-semibold text-muted-foreground">Administration</h2>
            <ul className="space-y-1">
              {menuSections.admin.map((item) => (
                <li key={item.label}>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium group relative border-l-2 border-orange-500/50",
                      pathname.startsWith(item.href)
                        ? "bg-orange-500/10 text-orange-400"
                        : "text-muted-foreground hover:bg-muted"
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                    <span className="flex-1">{item.label}</span>
                    {item.description && (
                      <div className="absolute left-full ml-2 w-48 p-2 rounded-md bg-popover text-sm hidden group-hover:block shadow-lg border z-50">
                        {item.description}
                      </div>
                    )}
                    {item.badge && (
                      <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2 py-0.5 rounded dark:bg-orange-900 dark:text-orange-300">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        )}
      </nav>

      {/* User Menu */}
      {user && (
        <div className="p-4 border-t border-border">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="w-full justify-start h-auto p-2">
                <div className="flex items-center space-x-2 w-full">
                  <div className="h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center">
                    <User className="h-4 w-4 text-primary" />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="text-sm font-medium text-foreground truncate">
                      {user.email}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Authenticated
                    </div>
                  </div>
                  <ChevronDown className="h-4 w-4 text-muted-foreground" />
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem disabled>
                <User className="mr-2 h-4 w-4" />
                <span>{user.email}</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={async () => {
                  try {
                    await signOut();
                    // Force reload to clear any cached state
                    window.location.reload();
                  } catch (error) {
                    console.error('Logout error:', error);
                    // Force reload anyway to clear state
                    window.location.reload();
                  }
                }}
                className="text-red-600 focus:text-red-600"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
    </div>
  )
}

