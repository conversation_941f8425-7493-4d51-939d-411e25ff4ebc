"use client";

import { useState, useCallback } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';

interface SyncStatus {
  last_sync?: string;
  last_sync_summary?: {
    total_retell_agents: number;
    total_db_agents: number;
    added: number;
    updated: number;
    removed: number;
    errors: number;
  };
  current_status: {
    retell_agents: number;
    db_agents_active: number;
    db_agents_inactive: number;
    sync_needed: boolean;
  };
}

interface SyncResult {
  success: boolean;
  summary: {
    total_retell_agents: number;
    total_db_agents: number;
    added: number;
    updated: number;
    removed: number;
    errors: number;
  };
  details: {
    added: Array<{ agent_id: string; name: string; organization_id: string }>;
    updated: Array<{ agent_id: string; changes: string[] }>;
    removed: Array<{ agent_id: string; reason: string }>;
    errors: Array<{ agent_id: string; error: string }>;
  };
  dry_run: boolean;
  timestamp: string;
}

export function useAgentSync() {
  const { organization } = useOrganization();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);

  // Get current sync status
  const getSyncStatus = useCallback(async () => {
    if (!organization?.id) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/sync/agents?organization_id=${organization.id}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get sync status');
      }

      const status = await response.json();
      setSyncStatus(status);
      return status;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get sync status';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [organization?.id]);

  // Perform sync (with optional dry run)
  const syncAgents = useCallback(async (dryRun = false) => {
    if (!organization?.id) {
      throw new Error('No organization selected');
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/sync/agents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          organization_id: organization.id,
          dry_run: dryRun
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Sync failed');
      }

      const result = await response.json();
      setLastSyncResult(result);

      // Refresh sync status after successful sync
      if (!dryRun && result.success) {
        await getSyncStatus();
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Sync failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [organization?.id, getSyncStatus]);

  // Preview sync changes (dry run)
  const previewSync = useCallback(async () => {
    return syncAgents(true);
  }, [syncAgents]);

  // Execute sync
  const executeSync = useCallback(async () => {
    return syncAgents(false);
  }, [syncAgents]);

  // Check if sync is needed
  const isSyncNeeded = useCallback(() => {
    return syncStatus?.current_status?.sync_needed || false;
  }, [syncStatus]);

  // Get time since last sync
  const getTimeSinceLastSync = useCallback(() => {
    if (!syncStatus?.last_sync) return null;
    
    const lastSync = new Date(syncStatus.last_sync);
    const now = new Date();
    const diffMs = now.getTime() - lastSync.getTime();
    
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays > 0) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    if (diffHours > 0) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    if (diffMinutes > 0) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  }, [syncStatus]);

  // Get sync health status
  const getSyncHealth = useCallback(() => {
    if (!syncStatus) return 'unknown';
    
    const { current_status, last_sync_summary } = syncStatus;
    
    // Check if sync is needed
    if (current_status.sync_needed) return 'needs_sync';
    
    // Check for recent errors
    if (last_sync_summary?.errors && last_sync_summary.errors > 0) return 'warning';
    
    // Check if never synced
    if (!syncStatus.last_sync) return 'never_synced';
    
    // Check if last sync was too long ago (more than 24 hours)
    const lastSync = new Date(syncStatus.last_sync);
    const now = new Date();
    const hoursSinceSync = (now.getTime() - lastSync.getTime()) / (1000 * 60 * 60);
    
    if (hoursSinceSync > 24) return 'stale';
    
    return 'healthy';
  }, [syncStatus]);

  return {
    // State
    loading,
    error,
    syncStatus,
    lastSyncResult,
    
    // Actions
    getSyncStatus,
    previewSync,
    executeSync,
    
    // Computed values
    isSyncNeeded: isSyncNeeded(),
    timeSinceLastSync: getTimeSinceLastSync(),
    syncHealth: getSyncHealth(),
    
    // Utilities
    clearError: () => setError(null),
    clearLastResult: () => setLastSyncResult(null)
  };
}
