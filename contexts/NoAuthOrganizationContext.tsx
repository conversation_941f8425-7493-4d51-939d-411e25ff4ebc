"use client";

import React, { createContext, useContext } from 'react';

// Mock organization data for development
const mockOrganization = {
  id: 'dev-org-123',
  name: 'Development Organization',
  slug: 'dev-org',
  plan: 'enterprise' as const,
  status: 'active' as const,
  settings: {},
  max_agents: 100,
  max_calls_per_month: 10000,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

const mockMembership = {
  id: 'dev-membership-123',
  organization_id: 'dev-org-123',
  user_id: 'dev-user-123',
  role: 'admin' as const,
  created_at: new Date().toISOString(),
};

const mockStats = {
  total_agents: 8,
  total_members: 1,
  current_month_calls: 150,
  current_month_minutes: 450,
};

interface Organization {
  id: string;
  name: string;
  slug: string;
  plan: 'free' | 'starter' | 'professional' | 'enterprise';
  status: 'active' | 'suspended' | 'cancelled';
  settings: Record<string, any>;
  max_agents: number;
  max_calls_per_month: number;
  created_at: string;
  updated_at: string;
}

interface OrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: 'admin' | 'member' | 'viewer';
  created_at: string;
}

interface OrganizationStats {
  total_agents: number;
  total_members: number;
  current_month_calls: number;
  current_month_minutes: number;
}

interface OrganizationContextType {
  organization: Organization | null;
  membership: OrganizationMember | null;
  stats: OrganizationStats | null;
  loading: boolean;
  error: string | null;
  isAdmin: boolean;
  isMember: boolean;
  canManageAgents: boolean;
  canViewAnalytics: boolean;
  refreshOrganization: () => Promise<void>;
  switchOrganization: (orgId: string) => Promise<void>;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export function NoAuthOrganizationProvider({ children }: { children: React.ReactNode }) {
  const refreshOrganization = async () => {
    console.log('Mock refresh organization');
    // Do nothing in development mode
  };

  const switchOrganization = async (orgId: string) => {
    console.log('Mock switch organization:', orgId);
    // Do nothing in development mode
  };

  const value: OrganizationContextType = {
    organization: mockOrganization,
    membership: mockMembership,
    stats: mockStats,
    loading: false,
    error: null,
    isAdmin: true,
    isMember: true,
    canManageAgents: true,
    canViewAnalytics: true,
    refreshOrganization,
    switchOrganization,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
}
