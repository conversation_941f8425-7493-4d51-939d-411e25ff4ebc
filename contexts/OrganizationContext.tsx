"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase/client';
import { User } from '@supabase/supabase-js';

interface Organization {
  id: string;
  name: string;
  slug: string;
  plan: 'free' | 'starter' | 'professional' | 'enterprise';
  status: 'active' | 'suspended' | 'cancelled';
  settings: Record<string, any>;
  max_agents: number;
  max_calls_per_month: number;
  created_at: string;
  updated_at: string;
}

interface OrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: 'admin' | 'member' | 'viewer';
  created_at: string;
}

interface OrganizationStats {
  total_agents: number;
  total_members: number;
  current_month_calls: number;
  current_month_minutes: number;
}

interface OrganizationContextType {
  organization: Organization | null;
  membership: OrganizationMember | null;
  stats: OrganizationStats | null;
  loading: boolean;
  error: string | null;
  isAdmin: boolean;
  isMember: boolean;
  canManageAgents: boolean;
  canViewAnalytics: boolean;
  refreshOrganization: () => Promise<void>;
  switchOrganization: (orgId: string) => Promise<void>;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export function OrganizationProvider({ children }: { children: React.ReactNode }) {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [membership, setMembership] = useState<OrganizationMember | null>(null);
  const [stats, setStats] = useState<OrganizationStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<User | null>(null);

  const fetchOrganizationData = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Get user's organization membership
      const { data: membershipData, error: membershipError } = await supabase
        .from('organization_members')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (membershipError) {
        if (membershipError.code === 'PGRST116') {
          // No organization membership found
          setOrganization(null);
          setMembership(null);
          setStats(null);
          setLoading(false);
          return;
        }
        throw membershipError;
      }

      setMembership(membershipData);

      // Get organization details
      const { data: orgData, error: orgError } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', membershipData.organization_id)
        .single();

      if (orgError) throw orgError;
      setOrganization(orgData);

      // Get organization stats
      const { data: statsData, error: statsError } = await supabase
        .from('organization_stats')
        .select('total_agents, total_members, current_month_calls, current_month_minutes')
        .eq('id', membershipData.organization_id)
        .single();

      if (statsError) {
        console.warn('Failed to fetch organization stats:', statsError);
        setStats(null);
      } else {
        setStats(statsData);
      }

    } catch (err) {
      console.error('Error fetching organization data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organization data');
    } finally {
      setLoading(false);
    }
  };

  const switchOrganization = async (orgId: string) => {
    // For now, users can only be in one organization
    // This function is for future multi-organization support
    console.log('Switching to organization:', orgId);
    await fetchOrganizationData();
  };

  // Get user on mount and listen for auth changes
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setUser(session?.user ?? null);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  useEffect(() => {
    fetchOrganizationData();
  }, [user]);

  // Computed properties
  const isAdmin = membership?.role === 'admin';
  const isMember = membership?.role === 'member' || isAdmin;
  const canManageAgents = isAdmin || membership?.role === 'member';
  const canViewAnalytics = isAdmin || isMember;

  const value: OrganizationContextType = {
    organization,
    membership,
    stats,
    loading,
    error,
    isAdmin,
    isMember,
    canManageAgents,
    canViewAnalytics,
    refreshOrganization: fetchOrganizationData,
    switchOrganization,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
}

// Hook for organization-aware API calls
export function useOrganizationApi() {
  const { organization } = useOrganization();

  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    if (!organization) {
      throw new Error('No organization context available');
    }

    const headers = {
      'Content-Type': 'application/json',
      'X-Organization-ID': organization.id,
      ...options.headers,
    };

    return fetch(endpoint, {
      ...options,
      headers,
    });
  };

  return { apiCall, organizationId: organization?.id };
}

// Component to protect routes that require organization membership
export function RequireOrganization({
  children,
  fallback
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { organization, loading, error } = useOrganization();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-2">Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!organization) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">No Organization</h2>
          <p className="text-gray-600">You need to be a member of an organization to access this page.</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Component to protect routes that require admin access
export function RequireAdmin({
  children,
  fallback
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { isAdmin, loading } = useOrganization();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-2">Access Denied</h2>
          <p className="text-gray-600">You need admin privileges to access this page.</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
