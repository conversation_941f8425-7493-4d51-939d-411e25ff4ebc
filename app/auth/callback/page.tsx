"use client";

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function AuthCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('Auth callback started');
        console.log('Current URL:', window.location.href);
        console.log('Search params:', Object.fromEntries(searchParams.entries()));
        console.log('Hash:', window.location.hash);

        // Get parameters from URL
        const code = searchParams.get('code');
        const token = searchParams.get('token');
        const type = searchParams.get('type');
        const error_code = searchParams.get('error_code');
        const error_description = searchParams.get('error_description');

        // Handle error cases first
        if (error_code) {
          console.error('Auth error from URL:', error_code, error_description);
          setError(error_description || 'Authentication failed');
          setLoading(false);
          return;
        }

        let authResult;

        // Handle new PKCE flow (code-based)
        if (code) {
          console.log('Handling PKCE flow with code:', code);
          authResult = await supabase.auth.exchangeCodeForSession(code);
        }
        // Handle legacy magic link flow (token-based)
        else if (token && type) {
          console.log('Handling magic link flow with token:', token, 'type:', type);
          authResult = await supabase.auth.verifyOtp({
            token_hash: token,
            type: 'magiclink'
          });
        }
        // Check if we have tokens in the URL hash (for magic links)
        else {
          const hashParams = new URLSearchParams(window.location.hash.substring(1));
          const accessToken = hashParams.get('access_token');
          const refreshToken = hashParams.get('refresh_token');

          if (accessToken && refreshToken) {
            console.log('Found tokens in hash, setting session');
            authResult = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            });
          } else {
            // Check if we already have a session
            const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

            if (sessionData?.session) {
              console.log('Already have valid session');
              setSuccess(true);
              setLoading(false);
              setTimeout(() => {
                router.push('/');
              }, 2000);
              return;
            } else {
              console.log('No session found, no tokens in URL');
              setError('No authentication data found in the URL');
              setLoading(false);
              return;
            }
          }
        }

        const { data, error: authError } = authResult;

        if (authError) {
          console.error('Auth callback error:', authError);
          setError(authError.message);
          setLoading(false);
          return;
        }

        if (data.user) {
          console.log('Authentication successful for user:', data.user.email);

          // Force session refresh to ensure cookies are properly set
          console.log('Refreshing session to ensure proper cookie setup...');
          await supabase.auth.refreshSession();

          // Try to ensure user is assigned to organization
          try {
            const response = await fetch('/api/auth/ensure-organization-assignment', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ userId: data.user.id, email: data.user.email }),
            });

            if (!response.ok) {
              console.warn('Failed to ensure organization assignment, but continuing...');
            } else {
              console.log('Organization assignment verified');
            }
          } catch (err) {
            console.warn('Organization assignment check failed, but continuing...', err);
          }

          // Multiple session verification attempts with retries
          let sessionValid = false;
          for (let i = 0; i < 5; i++) {
            const { data: sessionCheck } = await supabase.auth.getSession();
            if (sessionCheck.session) {
              sessionValid = true;
              console.log('Session verification successful on attempt', i + 1);
              break;
            }
            console.log('Session verification failed, attempt', i + 1, 'retrying...');
            await new Promise(resolve => setTimeout(resolve, 300)); // Wait 300ms between attempts
          }

          if (!sessionValid) {
            console.error('Session could not be established after multiple attempts');
            setError('Session could not be established. Please try signing in again.');
            setLoading(false);
            return;
          }

          setSuccess(true);
          setLoading(false);

          // Wait a moment to show success, then redirect to dashboard
          setTimeout(() => {
            router.push('/dashboard');
          }, 1000);
        } else {
          console.error('No user data in auth result');
          setError('Authentication failed - no user data received');
          setLoading(false);
        }

      } catch (err) {
        console.error('Callback handling error:', err);
        setError('An unexpected error occurred during authentication');
        setLoading(false);
      }
    };

    handleAuthCallback();
  }, [searchParams, router]);

  const handleRetry = () => {
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-950 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900 border-gray-800">
        <CardHeader className="text-center">
          <CardTitle className="text-white">
            {loading && 'Signing you in...'}
            {success && 'Welcome!'}
            {error && 'Authentication Error'}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center">
          {loading && (
            <div className="space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-500" />
              <p className="text-gray-400">
                Processing your authentication...
              </p>
            </div>
          )}

          {success && (
            <div className="space-y-4">
              <CheckCircle className="h-8 w-8 mx-auto text-green-500" />
              <div>
                <p className="text-green-400 font-medium">
                  Successfully signed in!
                </p>
                <p className="text-gray-400 text-sm mt-1">
                  Redirecting you to the dashboard...
                </p>
              </div>
            </div>
          )}

          {error && (
            <div className="space-y-4">
              <Alert className="border-red-800 bg-red-900/20">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">
                  {error}
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <p className="text-gray-400 text-sm">
                  Please try signing in again or contact support if the problem persists.
                </p>
                <Button onClick={handleRetry} className="w-full">
                  Return to Sign In
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
