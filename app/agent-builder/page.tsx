"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "lucide-react"
// import { Card } from "@/components/ui/card"
// import { Button } from "@/components/ui/button"
// import {
//   Brain,
//   MessageSquare,
//   Settings,
//   Trash2
// } from "lucide-react"
import CreateAgentButton from "@/components/agent-builder/CreateAgentButton"
import AgentSummaryModal from "@/components/agent-builder/AgentSummaryModal"
import { Avatar } from "@/components/ui/avatar"
import Image from "next/image"
import voicesList from "@/components/agent-builder/retellai-voices-list.json"
import { useToast } from "@/hooks/use-toast"

interface Agent {
  agent_id: string
  agent_name: string
  voice_id: string
  voice_model?: string
  language?: string
  response_engine?: {
    type: string
    llm_id: string
  }
  webhook_url?: string
  created_at?: string
  last_modification_timestamp?: string
  // Additional fields for UI
  description?: string
  status?: "active" | "inactive"
  knowledgeBases?: number
  totalCalls?: number
  voice?: string
  phone?: string
  editedBy?: string
  type?: string
  behavior?: string
}

interface Voice {
  voice_id: string;
  voice_name: string;
  provider: string;
  accent?: string;
  gender?: string;
  age?: string;
  avatar_url: string;
  preview_audio_url: string;
}

// Remove mock data - will fetch real data from API

// Voice avatar mapping from RetellAI voices list
const voiceAvatars = voicesList.reduce((acc, voice) => {
  acc[voice.voice_name] = {
    avatarUrl: voice.avatar_url,
    initials: voice.voice_name.substring(0, 2).toUpperCase(),
    color: "bg-gray-100 text-gray-800"
  }
  return acc
}, {} as Record<string, { avatarUrl: string, initials: string, color: string }>)

export default function AgentBuilderPage() {
  const [agents, setAgents] = useState<Agent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null)
  const [showAgentSummary, setShowAgentSummary] = useState(false)
  const [selectedVoiceData, setSelectedVoiceData] = useState<Voice | null>(null)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const { toast } = useToast()

  // Initialize audio reference
  if (typeof window !== 'undefined' && !audioRef.current) {
    audioRef.current = new Audio();
    audioRef.current.onended = () => {
      setIsPlaying(false);
    };
  }

  // Function to fetch agents from RetellAI API
  const fetchAgents = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/retell/agents')
      if (!response.ok) {
        throw new Error(`Failed to fetch agents: ${response.status}`)
      }

      const retellAgents = await response.json()
      console.log('Fetched RetellAI agents:', retellAgents)

      // Transform RetellAI agents to match our interface
      const transformedAgents: Agent[] = retellAgents.map((agent: any) => ({
        agent_id: agent.agent_id,
        agent_name: agent.agent_name,
        voice_id: agent.voice_id,
        voice_model: agent.voice_model,
        language: agent.language,
        response_engine: agent.response_engine,
        webhook_url: agent.webhook_url,
        created_at: agent.created_at,
        last_modification_timestamp: agent.last_modification_timestamp,
        // UI fields
        description: `AI agent powered by ${agent.voice_model || 'RetellAI'}`,
        status: "active" as const,
        knowledgeBases: 0, // TODO: Fetch from knowledge base API
        totalCalls: 0, // TODO: Fetch from analytics
        voice: getVoiceNameFromId(agent.voice_id),
        phone: "-", // TODO: Fetch associated phone numbers
        editedBy: agent.last_modification_timestamp ?
          new Date(agent.last_modification_timestamp).toLocaleDateString() :
          new Date(agent.created_at).toLocaleDateString(),
        type: "RetellAI Agent",
        behavior: "AI-powered voice agent" // TODO: Extract from LLM prompt
      }))

      setAgents(transformedAgents)
    } catch (err) {
      console.error('Error fetching agents:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch agents')
    } finally {
      setLoading(false)
    }
  }

  // Fetch agents on component mount
  useEffect(() => {
    fetchAgents()
  }, [])

  // Helper function to get voice name from voice ID
  const getVoiceNameFromId = (voiceId: string): string => {
    const voice = voicesList.find(v => v.voice_id === voiceId)
    return voice?.voice_name || voiceId
  }

  // Function to render voice avatar
  const renderVoiceAvatar = (voice: string) => {
    const avatar = voiceAvatars[voice] || {
      avatarUrl: "",
      initials: voice.substring(0, 2).toUpperCase(),
      color: "bg-gray-100 text-gray-800"
    }

    return (
      <Avatar className="h-8 w-8 border border-gray-200 shadow-sm overflow-hidden">
        {avatar.avatarUrl ? (
          <div className="relative h-full w-full">
            <Image
              src={avatar.avatarUrl}
              alt={voice}
              fill
              sizes="32px"
              className="object-cover"
              onError={(e) => {
                // Hide image on error and show initials instead
                e.currentTarget.style.display = 'none';
              }}
              priority={false}
              loading="lazy"
            />
            {/* Fallback initials */}
            <div className={`absolute inset-0 flex items-center justify-center text-xs font-medium ${avatar.color}`}>
              {avatar.initials}
            </div>
          </div>
        ) : (
          <div className={`h-full w-full flex items-center justify-center text-xs font-medium ${avatar.color}`}>
            {avatar.initials}
          </div>
        )}
      </Avatar>
    )
  }

  const handleAgentClick = (agent: Agent) => {
    setSelectedAgent(agent);

    // Find voice data if available
    if (agent.voice_id) {
      const voiceData = voicesList.find((voice: Voice) => voice.voice_id === agent.voice_id);
      setSelectedVoiceData(voiceData || null);
    } else {
      setSelectedVoiceData(null);
    }

    setShowAgentSummary(true);
  };

  const handleCloseAgentSummary = () => {
    setShowAgentSummary(false);
    setSelectedAgent(null);
    setSelectedVoiceData(null);
  };

  const handleEditAgent = () => {
    // Close the summary modal
    setShowAgentSummary(false);
    // TODO: Navigate to edit page when implemented
    console.log('Edit agent:', selectedAgent);
  };

  const handleDeleteAgent = async (agentId: string) => {
    try {
      const response = await fetch(`/api/retell/agents/${agentId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete agent');
      }

      // Refresh the agents list
      await fetchAgents();

      // Show success toast
      toast({
        title: "Agent Deleted",
        description: "The AI agent has been permanently deleted.",
        variant: "default",
      });

    } catch (error) {
      console.error('Error deleting agent:', error);

      // Show error toast
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete agent. Please try again.",
        variant: "destructive",
      });

      throw error; // Re-throw to let the dialog handle the error display
    }
  };





  const playVoiceSample = (url: string) => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.src = url;
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  return (
    <div className="p-8 max-w-[1600px] mx-auto">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-2xl font-semibold mb-2">AI Agent Builder</h1>
          <p className="text-muted-foreground">Create and manage your AI agents and their knowledge bases</p>
        </div>
        <CreateAgentButton />
      </div>

      {/* Existing Agents */}
      <h2 className="text-xl font-semibold mb-4">Your AI Agents</h2>
      <div className="w-full mb-6">
        <div className="grid grid-cols-12 gap-4 px-6 py-3 bg-gray-50 dark:bg-gray-800 rounded-t-lg">
          <div className="col-span-4">Agent Name</div>
          <div className="col-span-2">Agent Type</div>
          <div className="col-span-2">Voice</div>
          <div className="col-span-2">Phone</div>
          <div className="col-span-2">Edited by</div>
        </div>

        {loading ? (
          <div className="px-6 py-8 text-center text-muted-foreground">
            Loading agents...
          </div>
        ) : error ? (
          <div className="px-6 py-8 text-center text-red-500">
            Error: {error}
          </div>
        ) : agents.length === 0 ? (
          <div className="px-6 py-8 text-center text-muted-foreground">
            No agents found. Create your first agent to get started.
          </div>
        ) : (
          agents.map((agent) => (
          <div
            key={agent.agent_id}
            className="grid grid-cols-12 gap-4 px-6 py-4 border-b hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer"
            onClick={() => handleAgentClick(agent)}
          >
            <div className="col-span-4 flex items-center gap-3">
              <div className="p-2 rounded-full bg-primary/10">
                <Bot className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-medium">{agent.agent_name}</h3>
                <p className="text-sm text-muted-foreground">{agent.description}</p>
              </div>
            </div>
            <div className="col-span-2 flex items-center">{agent.type}</div>
            <div className="col-span-2 flex items-center gap-2">
              {agent.voice && renderVoiceAvatar(agent.voice)}
              <span>{agent.voice}</span>
            </div>
            <div className="col-span-2 flex items-center">{agent.phone}</div>
            <div className="col-span-2 flex items-center justify-between">
              <span>{agent.editedBy}</span>
              <button
                className="text-gray-500 hover:text-gray-700"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent row click
                  // Handle menu button click
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-5 w-5"><circle cx="12" cy="12" r="1"/><circle cx="19" cy="12" r="1"/><circle cx="5" cy="12" r="1"/></svg>
              </button>
            </div>
          </div>
          ))
        )}
      </div>

      {/* Add New Agent Button */}
      <div className="mt-4">
        <CreateAgentButton variant="primary" />
      </div>

      {/* Agent Summary Modal */}
      <AgentSummaryModal
        isOpen={showAgentSummary}
        onClose={handleCloseAgentSummary}
        agent={selectedAgent}
        voiceData={selectedVoiceData}
        onPlayVoiceSample={playVoiceSample}
        onEditAgent={handleEditAgent}
        onDeleteAgent={handleDeleteAgent}
      />
    </div>
  )
}