import "./globals.css"
import { Inter } from "next/font/google"
import { cn } from "@/lib/utils"
import Sidebar from "@/components/sidebar"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import ModalProviders from "@/components/providers/modal-providers"
import { TimeframeProvider } from "@/contexts/TimeframeContext"
import { AgentProvider } from "@/contexts/AgentContext"
import { OrganizationProvider } from "@/contexts/OrganizationContext"
import { AuthProvider } from "@/components/auth/AuthProvider"

const inter = Inter({ subsets: ["latin"] })

export const metadata = {
  title: "Voice Analytics Dashboard",
  description: "Monitor your voice agent performance",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning className="dark">
      <body className={cn(inter.className, "bg-background")}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          forcedTheme="dark"
          enableSystem={false}
          disableTransitionOnChange
        >
          <AuthProvider>
            <OrganizationProvider>
              <TimeframeProvider>
                <AgentProvider>
                  <div className="flex h-screen">
                    <Sidebar />
                    <main className="flex-1 overflow-auto">{children}</main>
                  </div>
                </AgentProvider>
              </TimeframeProvider>
            </OrganizationProvider>
          </AuthProvider>
        </ThemeProvider>

        {/* Modal providers */}
        <ModalProviders />

        {/* Toast notifications */}
        <Toaster />
      </body>
    </html>
  )
}



