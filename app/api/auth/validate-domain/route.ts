import { NextResponse } from 'next/server';
import { supabaseServiceRole } from '@/lib/supabase/service-role';

export async function POST(request: Request) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          allowed: false,
          reason: 'invalid_email',
          message: 'Please enter a valid email address.',
          email
        },
        { status: 400 }
      );
    }

    // Call the database function to validate domain
    const { data, error } = await supabaseServiceRole.rpc(
      'validate_user_registration',
      { email: email.toLowerCase().trim() }
    );

    if (error) {
      console.error('Domain validation error:', error);
      return NextResponse.json(
        {
          allowed: false,
          reason: 'validation_error',
          message: 'Unable to validate domain. Please try again.',
          email
        },
        { status: 500 }
      );
    }

    return NextResponse.json(data);

  } catch (error) {
    console.error('Domain validation request error:', error);
    return NextResponse.json(
      {
        allowed: false,
        reason: 'server_error',
        message: 'Server error occurred. Please try again.',
      },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  try {
    // Test database connection
    const { data, error } = await supabaseServiceRole
      .from('organization_domains')
      .select('count(*)')
      .limit(1);

    if (error) {
      return NextResponse.json(
        {
          status: 'error',
          message: 'Database connection failed',
          error: error.message
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      status: 'healthy',
      service: 'Domain Validation API',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    return NextResponse.json(
      {
        status: 'error',
        message: 'Service health check failed'
      },
      { status: 500 }
    );
  }
}
