import { NextResponse } from 'next/server';
import { supabaseServiceRole } from '@/lib/supabase/service-role';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';

// GET /api/admin/organizations - List all organizations
export async function GET(request: Request) {
  try {
    // Get URL parameters for pagination and filtering
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';

    const offset = (page - 1) * limit;

    // Build query
    let query = supabaseServiceRole
      .from('organizations')
      .select(`
        *,
        organization_domains (
          id,
          domain,
          verified,
          admin_approved,
          auto_assign_role,
          created_at
        ),
        organization_members (
          id,
          role,
          created_at
        )
      `, { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }
    if (status) {
      query = query.eq('status', status);
    }

    // Apply pagination
    query = query
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false });

    const { data: organizations, error, count } = await query;

    if (error) {
      console.error('Error fetching organizations:', error);
      return NextResponse.json(
        { error: 'Failed to fetch organizations' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      organizations,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Organizations list error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/admin/organizations - Create new organization
export async function POST(request: Request) {
  try {
    const body = await request.json();
    const {
      name,
      slug,
      plan = 'free',
      status = 'active',
      billing_email,
      max_agents = 3,
      max_calls_per_month = 1000,
      settings = {},
      domains = []
    } = body;

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Organization name is required' },
        { status: 400 }
      );
    }

    // Generate slug if not provided
    const organizationSlug = slug || name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Check if slug already exists
    const { data: existingOrg } = await supabaseServiceRole
      .from('organizations')
      .select('id')
      .eq('slug', organizationSlug)
      .single();

    if (existingOrg) {
      return NextResponse.json(
        { error: 'Organization slug already exists' },
        { status: 400 }
      );
    }

    // Create organization
    const { data: organization, error: orgError } = await supabaseServiceRole
      .from('organizations')
      .insert({
        name,
        slug: organizationSlug,
        plan,
        status,
        billing_email,
        max_agents,
        max_calls_per_month,
        settings
      })
      .select()
      .single();

    if (orgError) {
      console.error('Error creating organization:', orgError);
      return NextResponse.json(
        { error: 'Failed to create organization' },
        { status: 500 }
      );
    }

    // Validate and create domains if provided
    const createdDomains = [];
    const domainErrors = [];

    if (domains && domains.length > 0) {
      for (const domainData of domains) {
        const { domain, auto_assign_role = 'member', verified = false, admin_approved = false } = domainData;

        if (domain && domain.trim()) {
          // Validate domain before creating
          const { data: validation, error: validationError } = await supabaseServiceRole.rpc(
            'validate_domain_for_creation',
            {
              domain_input: domain.trim(),
              organization_id: organization.id
            }
          );

          if (validationError || !validation?.valid) {
            domainErrors.push({
              domain: domain.trim(),
              error: validation?.message || 'Domain validation failed'
            });
            continue;
          }

          // Create the domain record
          const { data: domainRecord, error: domainError } = await supabaseServiceRole
            .from('organization_domains')
            .insert({
              organization_id: organization.id,
              domain: validation.domain, // Use normalized domain
              verified,
              admin_approved,
              auto_assign_role,
              verification_method: 'manual',
              notes: 'Created via admin interface'
            })
            .select()
            .single();

          if (domainError) {
            domainErrors.push({
              domain: validation.domain,
              error: domainError.message
            });
          } else if (domainRecord) {
            createdDomains.push(domainRecord);
          }
        }
      }
    }

    // Log the creation
    await supabaseServiceRole.rpc('log_organization_change', {
      p_organization_id: organization.id,
      p_action: 'create',
      p_resource_type: 'organization',
      p_resource_id: organization.id,
      p_new_values: {
        name,
        slug: organizationSlug,
        plan,
        status,
        domains: createdDomains.length
      },
      p_metadata: {
        created_via: 'admin_interface',
        domains_created: createdDomains.length
      }
    });

    return NextResponse.json({
      organization: {
        ...organization,
        domains: createdDomains
      },
      domain_errors: domainErrors.length > 0 ? domainErrors : undefined,
      warnings: domainErrors.length > 0 ? [`${domainErrors.length} domain(s) could not be created due to validation errors`] : undefined,
      message: 'Organization created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Organization creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/organizations - Update organization (for future use)
export async function PUT(request: Request) {
  try {
    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Organization ID is required' },
        { status: 400 }
      );
    }

    const { data: organization, error } = await supabaseServiceRole
      .from('organizations')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating organization:', error);
      return NextResponse.json(
        { error: 'Failed to update organization' },
        { status: 500 }
      );
    }

    // Log the update
    await supabaseServiceRole.rpc('log_organization_change', {
      p_organization_id: id,
      p_action: 'update',
      p_resource_type: 'organization',
      p_resource_id: id,
      p_new_values: updateData,
      p_metadata: {
        updated_via: 'admin_interface'
      }
    });

    return NextResponse.json({
      organization,
      message: 'Organization updated successfully'
    });

  } catch (error) {
    console.error('Organization update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
