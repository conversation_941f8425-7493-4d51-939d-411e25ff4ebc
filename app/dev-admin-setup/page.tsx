"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Shield,
  User,
  CheckCircle,
  AlertCircle,
  Loader2,
  Building2,
  Users
} from "lucide-react";
import { useAuth } from "@/components/auth/AuthProvider";
import { useToast } from "@/hooks/use-toast";

interface AdminUser {
  user_id: string;
  role: string;
  joined_at: string;
  organization_id: string;
  organizations: {
    id: string;
    name: string;
    slug: string;
  };
  auth: {
    users: {
      id: string;
      email: string;
      created_at: string;
      last_sign_in_at: string;
    };
  };
}

export default function DevAdminSetupPage() {
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [makingAdmin, setMakingAdmin] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [email, setEmail] = useState('');
  const { user } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    if (user?.email) {
      setEmail(user.email);
    }
    fetchAdminUsers();
  }, [user]);

  const fetchAdminUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/check-admins');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch admin users');
      }

      setAdminUsers(data.adminUsers || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch admin users';
      setError(errorMessage);
      console.error('Fetch admin users error:', err);
    } finally {
      setLoading(false);
    }
  };

  const makeUserAdmin = async () => {
    if (!email) {
      setError('Email is required');
      return;
    }

    try {
      setMakingAdmin(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/admin/check-admins', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to make user admin');
      }

      setSuccess(data.message);
      toast({
        title: "Success",
        description: data.message,
      });

      // Refresh the admin users list
      await fetchAdminUsers();

      // Refresh the page to update the organization context
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to make user admin';
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setMakingAdmin(false);
    }
  };

  const isCurrentUserAdmin = adminUsers.some(
    admin => admin.auth?.users?.email === user?.email
  );

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Development Admin Setup</h1>
          <p className="text-muted-foreground mt-2">
            This page helps you set up admin privileges for development
          </p>
        </div>

        {/* Current User Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Current User Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {user ? (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span>Email:</span>
                  <span className="font-mono">{user.email}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Admin Status:</span>
                  {isCurrentUserAdmin ? (
                    <Badge variant="default" className="bg-green-600">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Admin
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Not Admin
                    </Badge>
                  )}
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">Not authenticated</p>
            )}
          </CardContent>
        </Card>

        {/* Make Admin */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Make User Admin
            </CardTitle>
            <CardDescription>
              Grant admin privileges to a user (for development purposes)
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <Alert className="border-red-800 bg-red-900/20">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-400">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="border-green-800 bg-green-900/20">
                <CheckCircle className="h-4 w-4" />
                <AlertDescription className="text-green-400">
                  {success}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter email address"
              />
            </div>

            <Button
              onClick={makeUserAdmin}
              disabled={makingAdmin || !email}
              className="w-full"
            >
              {makingAdmin ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Making Admin...
                </>
              ) : (
                <>
                  <Shield className="h-4 w-4 mr-2" />
                  Make Admin
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Current Admin Users */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Current Admin Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            ) : adminUsers.length > 0 ? (
              <div className="space-y-3">
                {adminUsers.map((admin, index) => (
                  <div
                    key={admin.user_id || `admin-${index}`}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <div className="font-medium">{admin.auth?.users?.email || 'Unknown Email'}</div>
                      <div className="text-sm text-muted-foreground">
                        {admin.organizations?.name || 'Unknown Organization'} • Joined {new Date(admin.joined_at).toLocaleDateString()}
                      </div>
                    </div>
                    <Badge variant="default" className="bg-green-600">
                      <Shield className="h-3 w-3 mr-1" />
                      Admin
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-muted-foreground text-center py-8">
                No admin users found
              </p>
            )}
          </CardContent>
        </Card>

        <div className="text-center text-sm text-muted-foreground">
          <p>⚠️ This page is for development purposes only</p>
          <p>Remove this page before deploying to production</p>
        </div>
      </div>
    </div>
  );
}
